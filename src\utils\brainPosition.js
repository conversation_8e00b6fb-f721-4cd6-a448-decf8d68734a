/**
 * 计算大脑形状位置
 * @param {Array} nodes - 原始节点数据
 * @param {number} startIndex - 全局索引起点
 * @returns {Array} processedNodes
 */
export function calculateBrainPositions(nodes, startIndex = 0) {
  const processedNodes = []

  // 大脑尺寸参数
  const brainWidth = 420
  const brainHeight = 400
  const brainDepth = 450

  // 不同脑区的颜色
  const nodeColors = {
    surface: '#33ffff',
    internal: '#21b8c0',
    highlight: '#5cffff'
  }

  for (let i = 0; i < nodes.length; i++) {
    const item = nodes[i]
    const globalIndex = startIndex + i
    const t = (globalIndex % 1000) / 1000 // 使用模运算避免超出范围

    let x = 0, y = 0, z = 0
    let nodeSize = 1.0
    let nodeColor = nodeColors.internal

    // 根据位置索引确定脑区
    if (t < 0.75) {
      // 大脑皮层 - 75%的节点
      const theta = Math.PI * 2 * Math.random()
      const phi = Math.acos(2 * Math.random() - 1)

      const isSurface = Math.random() > 0.7
      let radius

      if (isSurface) {
        radius = 0.85 + 0.15 * Math.random()
        nodeSize = 1.5
        nodeColor = nodeColors.surface
      } else {
        radius = Math.pow(Math.random(), 1/3) * 0.8
        nodeSize = 0.7
      }

      x = radius * Math.sin(phi) * Math.cos(theta)
      y = radius * Math.cos(phi)
      z = radius * Math.sin(phi) * Math.sin(theta)

      // 调整大脑形状
      x *= 1.1
      if (z > 0 && y > 0) z *= 1.3 // 前额叶
      if (z < 0 && Math.abs(y) < 0.3) z *= 1.2 // 视觉皮层

      x *= brainWidth * 0.45
      y *= brainHeight * 0.45
      z *= brainDepth * 0.4
      y += brainHeight * 0.1

    } else if (t < 0.9) {
      // 小脑 - 15%的节点
      const theta = Math.PI * (0.3 + 0.4 * Math.random())
      const phi = Math.PI * (0.6 + 0.3 * Math.random())

      const isSurface = Math.random() > 0.6
      let radius = isSurface ? 0.85 + 0.15 * Math.random() : Math.pow(Math.random(), 1/3) * 0.7

      x = radius * Math.sin(phi) * Math.cos(theta)
      y = radius * Math.cos(phi)
      z = -Math.abs(radius * Math.sin(phi) * Math.sin(theta))

      x *= brainWidth * 0.25
      y *= brainHeight * 0.25
      z *= brainDepth * 0.25
      y -= brainHeight * 0.15
      z -= brainDepth * 0.15

    } else {
      // 脑干 - 10%的节点
      const height = Math.random()
      const angle = Math.PI * 2 * Math.random()
      const radius = 0.3 * Math.random()

      x = radius * Math.cos(angle)
      y = -0.5 - height * 0.5
      z = -0.1 - radius * Math.sin(angle)

      x *= brainWidth * 0.15
      y *= brainHeight * 0.4
      z *= brainDepth * 0.2
      y -= brainHeight * 0.15
      z -= brainDepth * 0.1
    }

    // 添加随机抖动
    const jitter = 5
    x += (Math.random() - 0.5) * jitter
    y += (Math.random() - 0.5) * jitter
    z += (Math.random() - 0.5) * jitter

    // 创建节点对象
    processedNodes.push({
      id: Date.now() + Math.random() * 1000000, // 避免大数字问题
      user: item.keywords,
      color: nodeColor,
      bookId: item.book_keywords_id,
      nodeId: item.member_keywords_id,
      x, y, z,
      fx: x, fy: y, fz: z, // 固定位置
      nodeSize
    })
  }

  return processedNodes
}
