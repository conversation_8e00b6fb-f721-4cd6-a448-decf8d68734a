/**
 * 基于真实大脑解剖图像的大脑形状位置计算
 * 参考图像：蓝色透明大脑解剖图
 * @param {Array} nodes - 原始节点数据
 * @param {number} startIndex - 全局索引起点
 * @returns {Array} processedNodes
 */
export function calculateBrainPositions(nodes, startIndex = 0) {
  const processedNodes = []

  // 大脑尺寸参数 - 基于真实大脑比例
  const brainWidth = 420
  const brainHeight = 400
  const brainDepth = 450

  // 大脑区域配置 - 基于真实解剖结构
  const brainRegions = {
    // 大脑皮层 - 主要区域
    cortex: {
      ratio: 0.70, // 70%的节点
      areas: [
        { name: 'frontal', ratio: 0.35, density: 1.8, color: '#4A90E2' },      // 前额叶
        { name: 'parietal', ratio: 0.25, density: 1.5, color: '#34495E' },     // 顶叶
        { name: 'temporal', ratio: 0.25, density: 1.4, color: '#1ABC9C' },     // 颞叶
        { name: 'occipital', ratio: 0.15, density: 1.6, color: '#9B59B6' }     // 枕叶
      ]
    },
    // 小脑 - 高密度小体积
    cerebellum: {
      ratio: 0.20, // 20%的节点
      density: 2.5,
      color: '#E67E22'
    },
    // 脑干 - 连接结构
    brainstem: {
      ratio: 0.10, // 10%的节点
      density: 0.8,
      color: '#95A5A6'
    }
  }

  for (let i = 0; i < nodes.length; i++) {
    const item = nodes[i]
    const globalIndex = startIndex + i
    const t = (globalIndex % 10000) / 10000 // 扩大周期

    let x = 0, y = 0, z = 0
    let nodeSize = 1.0
    let nodeColor = '#21b8c0'
    let brainRegion = 'cortex'

    // 根据真实大脑解剖结构分配节点
    if (t < brainRegions.cortex.ratio) {
      // 大脑皮层 - 使用真实大脑形状
      const cortexResult = calculateRealisticCortexPosition(t / brainRegions.cortex.ratio, brainRegions.cortex.areas)
      x = cortexResult.x
      y = cortexResult.y
      z = cortexResult.z
      nodeSize = cortexResult.size
      nodeColor = cortexResult.color
      brainRegion = cortexResult.region

    } else if (t < brainRegions.cortex.ratio + brainRegions.cerebellum.ratio) {
      // 小脑 - 后下方高密度结构
      const cerebellumPos = calculateRealisticCerebellumPosition()
      x = cerebellumPos.x
      y = cerebellumPos.y
      z = cerebellumPos.z
      nodeSize = 0.6
      nodeColor = brainRegions.cerebellum.color
      brainRegion = 'cerebellum'

    } else {
      // 脑干 - 中央连接结构
      const brainstemPos = calculateRealisticBrainstemPosition()
      x = brainstemPos.x
      y = brainstemPos.y
      z = brainstemPos.z
      nodeSize = 0.9
      nodeColor = brainRegions.brainstem.color
      brainRegion = 'brainstem'
    }

    // 应用大脑整体尺寸
    x *= brainWidth * 0.5
    y *= brainHeight * 0.5
    z *= brainDepth * 0.5

    // 添加细微的随机抖动
    const jitter = 3
    x += (Math.random() - 0.5) * jitter
    y += (Math.random() - 0.5) * jitter
    z += (Math.random() - 0.5) * jitter

    // 创建节点对象
    processedNodes.push({
      id: Date.now() + Math.random() * 1000000,
      user: item.keywords,
      color: nodeColor,
      bookId: item.book_keywords_id,
      nodeId: item.member_keywords_id,
      x, y, z,
      fx: x, fy: y, fz: z, // 固定位置
      nodeSize,
      brainRegion, // 添加脑区标识
      density: getDensityByRegion(brainRegion)
    })
  }

  return processedNodes
}

/**
 * 计算真实大脑皮层位置 - 基于解剖图像
 * @param {number} t - 在皮层内的相对位置 (0-1)
 * @param {Array} areas - 皮层区域配置
 * @returns {Object} 位置和属性信息
 */
function calculateRealisticCortexPosition(t, areas) {
  // 确定当前节点属于哪个功能区
  let currentArea = areas[0]
  let cumulativeRatio = 0
  for (const area of areas) {
    cumulativeRatio += area.ratio
    if (t <= cumulativeRatio) {
      currentArea = area
      break
    }
  }

  // 生成基础球坐标
  const theta = Math.PI * 2 * Math.random()
  const phi = Math.acos(2 * Math.random() - 1)

  // 根据密度调整表面/内部分布
  const surfaceProbability = Math.min(0.6, currentArea.density / 2.0)
  const isSurface = Math.random() < surfaceProbability

  let radius
  if (isSurface) {
    radius = 0.88 + 0.12 * Math.random() // 表面节点
  } else {
    // 内部节点 - 密度越高越集中
    const densityFactor = currentArea.density / 2.0
    radius = Math.pow(Math.random(), 1 / (1 + densityFactor)) * 0.85
  }

  // 基础椭球坐标
  let x = radius * Math.sin(phi) * Math.cos(theta)
  let y = radius * Math.cos(phi)
  let z = radius * Math.sin(phi) * Math.sin(theta)

  // 应用真实大脑形状变换
  const brainShape = applyRealisticBrainShape(x, y, z, currentArea.name, isSurface)
  x = brainShape.x
  y = brainShape.y
  z = brainShape.z

  // 添加皮层褶皱效果
  if (isSurface) {
    const folds = addCorticalFolds(x, y, z, currentArea.name)
    x += folds.x
    y += folds.y
    z += folds.z
  }

  // 计算节点大小
  const baseSize = isSurface ? 1.3 : 0.8
  const densitySize = baseSize * (0.7 + currentArea.density * 0.3)

  return {
    x, y, z,
    size: densitySize,
    color: currentArea.color,
    region: currentArea.name,
    density: currentArea.density
  }
}

/**
 * 应用真实大脑形状变换 - 基于参考图像的解剖结构
 * @param {number} x - x坐标
 * @param {number} y - y坐标
 * @param {number} z - z坐标
 * @param {string} region - 脑区名称
 * @param {boolean} isSurface - 是否为表面节点
 * @returns {Object} 变换后的坐标
 */
function applyRealisticBrainShape(x, y, z, region, isSurface) {
  // 整体大脑形状调整 - 基于真实大脑比例

  // 1. 大脑整体椭球形状优化
  x *= 1.2   // 左右稍宽
  y *= 0.9   // 上下稍扁
  z *= 1.3   // 前后较长

  // 2. 前额叶区域 - 前方圆润突出（参考图像特征）
  if (region === 'frontal') {
    if (z > 0) {
      // 前额叶前方突出，呈圆润形状
      const frontFactor = 1.0 + 0.8 * Math.exp(-Math.pow((y + 0.1) * 1.5, 2))
      z *= frontFactor

      // 前额叶上方和前方的特殊形状
      if (y > 0) {
        y *= 1.15
        z *= 1.2
      }

      // 前额叶侧面稍微收缩
      if (Math.abs(x) > 0.6) {
        x *= 0.95
      }
    }
  }

  // 3. 颞叶区域 - 侧面和下方扩展（参考图像的颞叶形状）
  if (region === 'temporal') {
    if (Math.abs(x) > 0.3 && y < 0.3) {
      // 颞叶向侧面显著扩展
      x *= 1.4

      // 颞叶向下扩展，形成特有的弯曲
      if (y < 0) {
        y *= 1.3
        // 颞叶下方向内弯曲
        if (y < -0.3) {
          x *= 0.9
          z *= 0.95
        }
      }

      // 颞叶中部稍微内凹
      if (Math.abs(z) < 0.2) {
        x *= 0.92
      }
    }
  }

  // 4. 枕叶区域 - 后方突出（参考图像的后脑勺形状）
  if (region === 'occipital') {
    if (z < -0.1) {
      // 枕叶后方突出
      z *= 1.3

      // 枕叶上方形成圆顶
      if (y > 0) {
        y *= 1.1
        z *= 1.15
      }

      // 枕叶下方稍微收缩
      if (y < -0.2) {
        z *= 0.95
      }
    }
  }

  // 5. 顶叶区域 - 上方圆顶（参考图像的头顶形状）
  if (region === 'parietal') {
    if (y > 0.2) {
      // 顶叶上方圆顶形状
      y *= 1.2

      // 顶叶中央稍微突出
      if (Math.abs(x) < 0.5 && Math.abs(z) < 0.5) {
        y *= 1.15
      }

      // 顶叶边缘稍微收缩
      if (Math.abs(x) > 0.7 || Math.abs(z) > 0.7) {
        y *= 0.95
      }
    }
  }

  // 6. 整体形状优化 - 使大脑更加圆润自然
  const distanceFromCenter = Math.sqrt(x*x + y*y + z*z)
  if (distanceFromCenter > 0.85 && isSurface) {
    // 表面平滑化，避免尖锐边缘
    const smoothFactor = 0.92 + 0.16 * Math.random()
    x *= smoothFactor
    y *= smoothFactor
    z *= smoothFactor
  }

  // 7. 大脑沟回的基本形状
  if (isSurface) {
    // 添加大脑表面的自然凹凸
    const surfaceVariation = 0.02
    x += Math.sin(y * 15 + z * 10) * surfaceVariation
    y += Math.sin(x * 12 + z * 8) * surfaceVariation
    z += Math.sin(x * 10 + y * 14) * surfaceVariation
  }

  return { x, y, z }
}

/**
 * 添加皮层褶皱效果 - 模拟真实大脑皮层的沟回结构
 * @param {number} x - x坐标
 * @param {number} y - y坐标
 * @param {number} z - z坐标
 * @param {string} region - 脑区名称
 * @returns {Object} 褶皱偏移量
 */
function addCorticalFolds(x, y, z, region) {
  let foldX = 0, foldY = 0, foldZ = 0

  // 基础褶皱参数
  const foldIntensity = 0.025
  const foldFrequency = 12

  // 不同脑区的褶皱特征
  switch (region) {
    case 'frontal':
      // 前额叶 - 水平和垂直褶皱
      foldX += Math.sin(y * foldFrequency) * foldIntensity
      foldY += Math.sin(z * foldFrequency * 1.2) * foldIntensity * 0.8
      foldZ += Math.sin(x * foldFrequency * 0.8) * foldIntensity * 0.6
      break

    case 'parietal':
      // 顶叶 - 放射状褶皱
      const angle = Math.atan2(z, x)
      foldX += Math.sin(angle * 8 + y * 6) * foldIntensity * 0.8
      foldZ += Math.cos(angle * 8 + y * 6) * foldIntensity * 0.8
      foldY += Math.sin(y * foldFrequency * 1.5) * foldIntensity * 0.5
      break

    case 'temporal':
      // 颞叶 - 纵向褶皱
      foldY += Math.sin(z * foldFrequency * 1.5) * foldIntensity
      foldZ += Math.sin(y * foldFrequency) * foldIntensity * 0.7
      foldX += Math.sin((y + z) * foldFrequency * 0.6) * foldIntensity * 0.5
      break

    case 'occipital':
      // 枕叶 - 同心圆褶皱
      const radius = Math.sqrt(x*x + y*y)
      foldX += Math.sin(radius * foldFrequency * 2.5) * foldIntensity * 0.6
      foldY += Math.cos(radius * foldFrequency * 2.5) * foldIntensity * 0.6
      foldZ += Math.sin(z * foldFrequency * 1.2) * foldIntensity
      break
  }

  // 添加随机微小变化，使褶皱更自然
  foldX += (Math.random() - 0.5) * foldIntensity * 0.4
  foldY += (Math.random() - 0.5) * foldIntensity * 0.4
  foldZ += (Math.random() - 0.5) * foldIntensity * 0.4

  return { x: foldX, y: foldY, z: foldZ }
}

/**
 * 计算真实小脑位置 - 基于解剖图像的小脑形状
 * @returns {Object} 位置信息
 */
function calculateRealisticCerebellumPosition() {
  // 小脑位于大脑后下方，具有特殊的褶皱结构
  const theta = Math.PI * (0.1 + 0.8 * Math.random()) // 更宽的角度范围
  const phi = Math.PI * (0.4 + 0.5 * Math.random())

  // 小脑密度极高，使用更复杂的分布
  const radius = Math.pow(Math.random(), 1/3) * 0.9 // 更密集的分布

  let x = radius * Math.sin(phi) * Math.cos(theta)
  let y = radius * Math.cos(phi)
  let z = -Math.abs(radius * Math.sin(phi) * Math.sin(theta)) // 始终在后方

  // 小脑特有的形状调整 - 基于参考图像
  // 小脑呈现横向扁平的形状
  x *= 0.8  // 小脑宽度
  y = y * 0.6 - 0.5  // 小脑位置下移
  z = z * 0.7 - 0.4  // 小脑位置后移

  // 小脑特有的褶皱结构 - 更密集的褶皱
  const foldFrequency = 25
  const foldAmplitude = 0.04
  x += Math.sin(phi * foldFrequency) * foldAmplitude
  y += Math.sin(theta * foldFrequency) * foldAmplitude * 0.5
  z += Math.cos(phi * foldFrequency * 1.2) * foldAmplitude * 0.3

  // 小脑的分叶结构
  if (Math.abs(x) > 0.3) {
    // 小脑半球
    y += Math.sin(x * 20) * 0.02
    z += Math.cos(x * 15) * 0.015
  }

  return { x, y, z }
}

/**
 * 计算真实脑干位置 - 基于解剖图像的脑干形状
 * @returns {Object} 位置信息
 */
function calculateRealisticBrainstemPosition() {
  // 脑干是连接大脑和脊髓的细长结构
  const height = Math.random() // 沿着脑干高度分布
  const angle = Math.PI * 2 * Math.random()
  const radius = 0.12 * Math.random() // 很细的结构

  let x = radius * Math.cos(angle)
  let y = -0.7 - height * 0.5 // 在底部，更深的位置
  let z = -0.05 + radius * Math.sin(angle) * 0.5 // 稍微向后

  // 脑干的分段结构
  if (height < 0.3) {
    // 延髓 - 最下方
    x *= 0.8
    y -= 0.1
  } else if (height < 0.7) {
    // 脑桥 - 中间部分，稍微粗一些
    x *= 1.2
    z -= 0.02
  } else {
    // 中脑 - 上方部分
    x *= 0.9
    y += 0.05
  }

  return { x, y, z }
}

/**
 * 根据脑区获取密度值
 * @param {string} region - 脑区名称
 * @returns {number} 密度值
 */
function getDensityByRegion(region) {
  const densityMap = {
    frontal: 1.8,
    parietal: 1.5,
    temporal: 1.4,
    occipital: 1.6,
    cerebellum: 2.5,
    brainstem: 0.8
  }
  return densityMap[region] || 1.0
}
