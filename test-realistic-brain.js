/**
 * 真实大脑形状算法测试脚本
 * 验证基于解剖图像的大脑形状生成效果
 */

// 模拟节点数据生成
function generateTestNodes(count = 1000) {
  const nodes = []
  for (let i = 0; i < count; i++) {
    nodes.push({
      keywords: `神经元${i}`,
      book_keywords_id: i,
      member_keywords_id: i
    })
  }
  return nodes
}

// 简化版的真实大脑形状算法（用于测试）
function calculateRealisticBrainPositions(nodes) {
  const processedNodes = []
  const brainWidth = 420
  const brainHeight = 400
  const brainDepth = 450

  // 脑区配置
  const brainRegions = {
    cortex: {
      ratio: 0.70,
      areas: [
        { name: 'frontal', ratio: 0.35, density: 1.8, color: '#4A90E2' },
        { name: 'parietal', ratio: 0.25, density: 1.5, color: '#34495E' },
        { name: 'temporal', ratio: 0.25, density: 1.4, color: '#1ABC9C' },
        { name: 'occipital', ratio: 0.15, density: 1.6, color: '#9B59B6' }
      ]
    },
    cerebellum: { ratio: 0.20, density: 2.5, color: '#E67E22' },
    brainstem: { ratio: 0.10, density: 0.8, color: '#95A5A6' }
  }

  for (let i = 0; i < nodes.length; i++) {
    const item = nodes[i]
    const t = i / nodes.length

    let x = 0, y = 0, z = 0
    let nodeSize = 1.0
    let nodeColor = '#21b8c0'
    let brainRegion = 'cortex'

    if (t < brainRegions.cortex.ratio) {
      // 大脑皮层
      const cortexT = t / brainRegions.cortex.ratio
      let currentArea = brainRegions.cortex.areas[0]
      let cumulativeRatio = 0
      
      for (const area of brainRegions.cortex.areas) {
        cumulativeRatio += area.ratio
        if (cortexT <= cumulativeRatio) {
          currentArea = area
          break
        }
      }

      // 生成基础坐标
      const theta = Math.PI * 2 * Math.random()
      const phi = Math.acos(2 * Math.random() - 1)
      const radius = Math.pow(Math.random(), 1 / (1 + currentArea.density / 2)) * 0.85

      x = radius * Math.sin(phi) * Math.cos(theta)
      y = radius * Math.cos(phi)
      z = radius * Math.sin(phi) * Math.sin(theta)

      // 应用真实大脑形状
      x *= 1.2
      y *= 0.9
      z *= 1.3

      // 根据脑区调整形状
      switch (currentArea.name) {
        case 'frontal':
          if (z > 0) {
            const frontFactor = 1.0 + 0.8 * Math.exp(-Math.pow((y + 0.1) * 1.5, 2))
            z *= frontFactor
            if (y > 0) {
              y *= 1.15
              z *= 1.2
            }
          }
          break
        case 'temporal':
          if (Math.abs(x) > 0.3 && y < 0.3) {
            x *= 1.4
            if (y < 0) {
              y *= 1.3
              if (y < -0.3) {
                x *= 0.9
              }
            }
          }
          break
        case 'occipital':
          if (z < -0.1) {
            z *= 1.3
            if (y > 0) {
              y *= 1.1
              z *= 1.15
            }
          }
          break
        case 'parietal':
          if (y > 0.2) {
            y *= 1.2
            if (Math.abs(x) < 0.5 && Math.abs(z) < 0.5) {
              y *= 1.15
            }
          }
          break
      }

      nodeSize = 0.8 + currentArea.density * 0.3
      nodeColor = currentArea.color
      brainRegion = currentArea.name

    } else if (t < brainRegions.cortex.ratio + brainRegions.cerebellum.ratio) {
      // 小脑
      const theta = Math.PI * (0.1 + 0.8 * Math.random())
      const phi = Math.PI * (0.4 + 0.5 * Math.random())
      const radius = Math.pow(Math.random(), 1/3) * 0.9

      x = radius * Math.sin(phi) * Math.cos(theta) * 0.8
      y = radius * Math.cos(phi) * 0.6 - 0.5
      z = -Math.abs(radius * Math.sin(phi) * Math.sin(theta)) * 0.7 - 0.4

      nodeSize = 0.6
      nodeColor = brainRegions.cerebellum.color
      brainRegion = 'cerebellum'

    } else {
      // 脑干
      const height = Math.random()
      const angle = Math.PI * 2 * Math.random()
      const radius = 0.12 * Math.random()

      x = radius * Math.cos(angle)
      y = -0.7 - height * 0.5
      z = -0.05 + radius * Math.sin(angle) * 0.5

      nodeSize = 0.9
      nodeColor = brainRegions.brainstem.color
      brainRegion = 'brainstem'
    }

    // 应用整体尺寸
    x *= brainWidth * 0.5
    y *= brainHeight * 0.5
    z *= brainDepth * 0.5

    processedNodes.push({
      id: i,
      user: item.keywords,
      x, y, z,
      nodeSize,
      color: nodeColor,
      brainRegion,
      density: getDensityByRegion(brainRegion)
    })
  }

  return processedNodes
}

function getDensityByRegion(region) {
  const densityMap = {
    frontal: 1.8,
    parietal: 1.5,
    temporal: 1.4,
    occipital: 1.6,
    cerebellum: 2.5,
    brainstem: 0.8
  }
  return densityMap[region] || 1.0
}

// 分析大脑形状特征
function analyzeBrainShape(nodes) {
  const regionStats = {}
  const shapeMetrics = {
    frontalExtent: { min: Infinity, max: -Infinity },
    temporalWidth: { min: Infinity, max: -Infinity },
    occipitalDepth: { min: Infinity, max: -Infinity },
    parietalHeight: { min: Infinity, max: -Infinity }
  }

  nodes.forEach(node => {
    const region = node.brainRegion
    if (!regionStats[region]) {
      regionStats[region] = {
        name: region,
        count: 0,
        totalDensity: 0,
        avgDensity: 0,
        bounds: {
          x: { min: Infinity, max: -Infinity },
          y: { min: Infinity, max: -Infinity },
          z: { min: Infinity, max: -Infinity }
        }
      }
    }

    const stat = regionStats[region]
    stat.count++
    stat.totalDensity += node.density

    // 更新边界
    stat.bounds.x.min = Math.min(stat.bounds.x.min, node.x)
    stat.bounds.x.max = Math.max(stat.bounds.x.max, node.x)
    stat.bounds.y.min = Math.min(stat.bounds.y.min, node.y)
    stat.bounds.y.max = Math.max(stat.bounds.y.max, node.y)
    stat.bounds.z.min = Math.min(stat.bounds.z.min, node.z)
    stat.bounds.z.max = Math.max(stat.bounds.z.max, node.z)

    // 分析形状特征
    if (region === 'frontal' && node.z > 0) {
      shapeMetrics.frontalExtent.min = Math.min(shapeMetrics.frontalExtent.min, node.z)
      shapeMetrics.frontalExtent.max = Math.max(shapeMetrics.frontalExtent.max, node.z)
    }
    if (region === 'temporal') {
      shapeMetrics.temporalWidth.min = Math.min(shapeMetrics.temporalWidth.min, Math.abs(node.x))
      shapeMetrics.temporalWidth.max = Math.max(shapeMetrics.temporalWidth.max, Math.abs(node.x))
    }
    if (region === 'occipital' && node.z < 0) {
      shapeMetrics.occipitalDepth.min = Math.min(shapeMetrics.occipitalDepth.min, Math.abs(node.z))
      shapeMetrics.occipitalDepth.max = Math.max(shapeMetrics.occipitalDepth.max, Math.abs(node.z))
    }
    if (region === 'parietal' && node.y > 0) {
      shapeMetrics.parietalHeight.min = Math.min(shapeMetrics.parietalHeight.min, node.y)
      shapeMetrics.parietalHeight.max = Math.max(shapeMetrics.parietalHeight.max, node.y)
    }
  })

  // 计算平均密度
  Object.values(regionStats).forEach(stat => {
    stat.avgDensity = stat.count > 0 ? stat.totalDensity / stat.count : 0
  })

  return { regionStats, shapeMetrics }
}

// 主测试函数
function testRealisticBrainShape() {
  console.log('🧠 真实大脑形状算法测试')
  console.log('=' .repeat(60))

  // 生成测试数据
  const testNodes = generateTestNodes(2000)
  console.log(`📊 生成测试节点: ${testNodes.length}个`)

  // 生成真实大脑形状
  console.log('\n🔬 生成真实大脑形状...')
  const brainNodes = calculateRealisticBrainPositions(testNodes)
  
  // 分析结果
  const analysis = analyzeBrainShape(brainNodes)
  
  console.log('\n📈 脑区分布分析:')
  Object.values(analysis.regionStats).forEach(stat => {
    const percentage = (stat.count / brainNodes.length * 100).toFixed(1)
    console.log(`  ${stat.name}: ${stat.count}个节点 (${percentage}%), 平均密度: ${stat.avgDensity.toFixed(2)}`)
    
    const width = stat.bounds.x.max - stat.bounds.x.min
    const height = stat.bounds.y.max - stat.bounds.y.min
    const depth = stat.bounds.z.max - stat.bounds.z.min
    console.log(`    尺寸: ${width.toFixed(1)} × ${height.toFixed(1)} × ${depth.toFixed(1)}`)
  })

  console.log('\n🎯 形状特征分析:')
  const metrics = analysis.shapeMetrics
  console.log(`  前额叶突出度: ${(metrics.frontalExtent.max - metrics.frontalExtent.min).toFixed(1)}`)
  console.log(`  颞叶宽度: ${(metrics.temporalWidth.max - metrics.temporalWidth.min).toFixed(1)}`)
  console.log(`  枕叶深度: ${(metrics.occipitalDepth.max - metrics.occipitalDepth.min).toFixed(1)}`)
  console.log(`  顶叶高度: ${(metrics.parietalHeight.max - metrics.parietalHeight.min).toFixed(1)}`)

  // 验证解剖学特征
  console.log('\n✅ 解剖学特征验证:')
  const frontalProtrusion = metrics.frontalExtent.max > 200
  const temporalExpansion = metrics.temporalWidth.max > 200
  const occipitalExtension = metrics.occipitalDepth.max > 150
  const parietalDome = metrics.parietalHeight.max > 150

  console.log(`  前额叶前方突出: ${frontalProtrusion ? '✓' : '✗'}`)
  console.log(`  颞叶侧面扩展: ${temporalExpansion ? '✓' : '✗'}`)
  console.log(`  枕叶后方延伸: ${occipitalExtension ? '✓' : '✗'}`)
  console.log(`  顶叶上方圆顶: ${parietalDome ? '✓' : '✗'}`)

  const overallScore = [frontalProtrusion, temporalExpansion, occipitalExtension, parietalDome]
    .filter(Boolean).length / 4 * 100

  console.log(`\n🏆 整体解剖学准确度: ${overallScore.toFixed(1)}%`)

  // 生成报告
  const report = {
    timestamp: new Date().toISOString(),
    nodeCount: brainNodes.length,
    regionDistribution: analysis.regionStats,
    shapeMetrics: metrics,
    anatomicalAccuracy: overallScore
  }

  console.log('\n📋 完整测试报告:')
  console.log(JSON.stringify(report, null, 2))

  return report
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    generateTestNodes,
    calculateRealisticBrainPositions,
    analyzeBrainShape,
    testRealisticBrainShape
  }
} else if (typeof window !== 'undefined') {
  window.RealisticBrainTest = {
    generateTestNodes,
    calculateRealisticBrainPositions,
    analyzeBrainShape,
    testRealisticBrainShape
  }
  
  console.log('🚀 自动运行真实大脑形状测试...')
  testRealisticBrainShape()
}
