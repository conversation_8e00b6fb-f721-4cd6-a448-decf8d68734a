import request from '@/http/request'
import { fetchEventSource } from '@microsoft/fetch-event-source'

// import { createSSEPromiseConnection } from '../http/sseClient'
const apiPath = '/api/Nebula/keywordsListAll'

const repeat = 1

export const getData3 = () => {
  return;
  fetchEventSource(apiPath, {
    headers: {
      Authorization: localStorage.getItem('authorization') || '',
    },
    // method: "POST",
    // signal: ctrl.signal,
    onmessage(ev) {
      let parsed
      // console.log(ev.data, ev)
      if (ev.data === '[]') {
        console.error('abort')
        // ctrl.abort()
      }
      parsed = JSON.parse(ev.data)
      console.log(parsed.length)
      if (parsed?.length > 0 && !window.__forceData.nodes) {
        console.log('开始加载处理',)
      return
        window.__forceData.nodes = parsed;
        window.__forceData.links = parsed;
      }
      return
      // onMessage?.(parsed)
    },
  })
}

export const getData2 = () => {
  console.log('getdata before')
  if(window.__forceData) return
  console.log('getdata')
  // const ctrl = new AbortController()
  console.log('(1)load data begin', new Date())
  // const {promise, abort} = createSSEPromiseConnection({
  //   url: apiPath,
  //   onMessage(msg) {
  //     console.log('999')
  //     console.log(msg)
  //   }
  // })
  // console.log(promise,)
  //  return request('/api/Nebula/newKeywordsList', {}).then(async (result) => {
  return
  return request('/api/Nebula/keywordsListAll', {}).then(async (result) => {
    // const list = result;

    console.log('(2)preload successfully', new Date())
    // 重复result数据模拟大数据
    // const list = Array(repeat).fill(result).flat()
    const list = result

    const nodes = []
    for (let i = 0; i < list.length; i += 1) {
      const item = list[i]
      nodes.push({
        // id: parseInt(Math.random() * 100000000000000000),
        id: item.book_keywords_id,
        user: item.keywords,
        // color: getRandomColor(),
        bookId: item.book_keywords_id,
        nodeId: item.member_keywords_id,
      })
    }
    // Pre-calculate positions for nodes
    const nodes2 = calculateNodePositions(nodes)
    // const nodes2 = nodes;

    const linksRes = await request('/api/Nebula/keywordsLinesAll', {})
    console.log(linksRes)

    // Create brain-like neural connections
    // const links = createBrainConnections(nodes2)
    const links = linksRes.map((item) => ({
      source: item.keywords_id_front,
      target: item.keywords_id_behind,
      color: 'cyan',
      width: 1,
    }))

    // Update the global forceData with links
    const forceData = {
      nodes: nodes2,
      links,
    }
    console.log('(3)convert data done', new Date())
    console.log(links)
    window.__forceData = forceData
    return forceData
  })
}

const calculateNodePositions = (_nodes) => {
  // If there are no nodes, return empty array
  if (!_nodes.length) return []
  let nodes = []

  const NODE_COUNT = _nodes.length

  // Different colors for different brain regions to enhance outline
  const nodeColors = {
    surface: '#33ffff', // Brighter cyan for surface nodes
    internal: '#21b8c0', // Darker cyan for internal nodes
    highlight: '#5cffff', // Extra bright for outline highlights
  }

  // Brain dimensions - unified brain without split
  const brainWidth = 420
  const brainHeight = 400
  const brainDepth = 450

  // For 3D solid brain, we'll use a volumetric approach with emphasis on outline
  for (let i = 0; i < NODE_COUNT; i++) {
    // Base parameter
    const t = i / NODE_COUNT

    // Initial position
    let x = 0,
      y = 0,
      z = 0

    // Use volumetric distribution to fill the entire brain
    const regionSeed = Math.random() // Add some randomness to distribution
    let nodeSize = 1.0 // Default node size
    let nodeColor = nodeColors.internal

    if (t < 0.75) {
      // Cerebrum (main part) - 75% of nodes
      // Create a solid unified cerebrum

      // Volumetric distribution within ellipsoid
      const theta = Math.PI * 2 * Math.random() // Random angle around y-axis
      const phi = Math.acos(2 * Math.random() - 1) // Angle from y-axis

      // Differentiate between surface and internal nodes
      // Use cube root for better volume distribution, but with surface emphasis
      let radius
      const isSurface = Math.random() > 0.7 // 30% of nodes are surface nodes

      if (isSurface) {
        // Surface nodes - closer to the edge
        radius = 0.85 + 0.15 * Math.random()
        nodeSize = 1.5 // Larger surface nodes
        nodeColor = nodeColors.surface
      } else {
        // Internal nodes - throughout the volume
        radius = Math.pow(Math.random(), 1 / 3) * 0.8 // Keep internal nodes away from surface
        nodeSize = 0.7 // Smaller internal nodes
      }

      // Base ellipsoid shape for unified cerebrum
      x = radius * Math.sin(phi) * Math.cos(theta)
      y = radius * Math.cos(phi)
      z = radius * Math.sin(phi) * Math.sin(theta)

      // Create the 3D shape of the brain
      // Make it slightly wider than tall
      x *= 1.1

      // Create the frontal lobe protrusion
      if (z > 0 && y > 0) {
        z *= 1.3
      }

      // Create the visual cortex at the back
      if (z < 0 && Math.abs(y) < 0.3) {
        z *= 1.2
      }

      // Create subtle surface details without splitting
      if (isSurface) {
        const detailFreq = 15
        const detailAmp = 0.08

        // Surface details only for surface nodes
        x += Math.sin(phi * detailFreq) * detailAmp * Math.random()
        y += Math.sin(theta * detailFreq) * detailAmp * Math.random()
        z += Math.cos(phi * detailFreq) * detailAmp * Math.random()
      }

      // Apply final cerebrum scaling
      x *= brainWidth * 0.45
      y *= brainHeight * 0.45
      z *= brainDepth * 0.4

      // Shift to position correctly
      y += brainHeight * 0.1
    } else if (t < 0.9) {
      // Cerebellum (the rounded structure at the bottom back) - 15% of nodes

      // Volumetric distribution for cerebellum
      const theta = Math.PI * (0.3 + 0.4 * Math.random()) // Limited angle for placement
      const phi = Math.PI * (0.6 + 0.3 * Math.random()) // Bottom back quadrant

      // Differentiate between surface and internal nodes
      const isSurface = Math.random() > 0.6 // 40% of cerebellum nodes are surface

      let radius
      if (isSurface) {
        radius = 0.85 + 0.15 * Math.random()
        nodeSize = 1.5 // Larger surface nodes
        nodeColor = nodeColors.surface
      } else {
        radius = Math.pow(Math.random(), 1 / 3) * 0.7
        nodeSize = 0.7 // Smaller internal nodes
      }

      // Base shape
      x = radius * Math.sin(phi) * Math.cos(theta)
      y = radius * Math.cos(phi)
      z = -Math.abs(radius * Math.sin(phi) * Math.sin(theta)) // Always in the back

      // Create the distinctive ridges of the cerebellum
      if (isSurface) {
        const ridgeFreq = 25 // Higher frequency for the dense cerebellum ridges
        const ridgeAmp = 0.06

        x += Math.sin(phi * ridgeFreq) * ridgeAmp
        y += Math.sin(theta * ridgeFreq) * ridgeAmp
      }

      // Apply cerebellum scaling - smaller than cerebrum
      x *= brainWidth * 0.25
      y *= brainHeight * 0.25
      z *= brainDepth * 0.25

      // Position the cerebellum correctly
      y -= brainHeight * 0.15 // Lower
      z -= brainDepth * 0.15 // More to the back
    } else {
      // Brain stem and lower structures - 10% of nodes

      // Volumetric distribution for brain stem
      const height = Math.random()
      const angle = Math.PI * 2 * Math.random()
      const radius = 0.3 * Math.random()

      // For brain stem, use standard size
      nodeSize = 1.0
      nodeColor = nodeColors.internal

      // Stem shape
      x = radius * Math.cos(angle)
      y = -0.5 - height * 0.5 // Extend downward
      z = -0.1 - radius * Math.sin(angle) // Slightly to the back

      // Apply stem scaling
      x *= brainWidth * 0.15
      y *= brainHeight * 0.4
      z *= brainDepth * 0.2

      // Position the stem
      y -= brainHeight * 0.15
      z -= brainDepth * 0.1
    }

    // Add small random variation for natural 3D look
    const jitter = 5
    x += (Math.random() - 0.5) * jitter
    y += (Math.random() - 0.5) * jitter
    z += (Math.random() - 0.5) * jitter

    // Add extra nodes at key outline positions to emphasize brain shape
    // These are specifically positioned to enhance the brain silhouette
    if (i % 50 === 0) {
      const outlinePoints = [
        // Top of brain
        { x: 0, y: brainHeight * 0.5, z: 0 },
        // Frontal lobe
        { x: 0, y: brainHeight * 0.4, z: brainDepth * 0.4 },
        // Back of brain
        { x: 0, y: brainHeight * 0.3, z: -brainDepth * 0.4 },
        // Temporal lobes (sides)
        { x: brainWidth * 0.45, y: 0, z: 0 },
        { x: -brainWidth * 0.45, y: 0, z: 0 },
        // Cerebellum
        { x: 0, y: -brainHeight * 0.3, z: -brainDepth * 0.3 },
      ]

      // Select one of the outline points
      const outlinePoint = outlinePoints[i % outlinePoints.length]

      // Add some variation to the outline point
      const outlineJitter = 20
      x = outlinePoint.x + (Math.random() - 0.5) * outlineJitter
      y = outlinePoint.y + (Math.random() - 0.5) * outlineJitter
      z = outlinePoint.z + (Math.random() - 0.5) * outlineJitter

      // Make outline nodes larger and brighter
      nodeSize = 2.0
      nodeColor = nodeColors.highlight
    }

    // Add the node with fixed position
    nodes.push({
      ..._nodes[i],
      x,
      y,
      z,
      fx: x,
      fy: y,
      fz: z,
      color: nodeColor,
      nodeSize: nodeSize, // Store node size
    })
  }

  return nodes
}

// 简单三维网格分桶加速最近邻查找
function buildSpatialGrid(nodes, cellSize = 50) {
  const grid = new Map()
  for (const node of nodes) {
    const key = [
      Math.floor(node.x / cellSize),
      Math.floor(node.y / cellSize),
      Math.floor(node.z / cellSize),
    ].join(',')
    if (!grid.has(key)) grid.set(key, [])
    grid.get(key).push(node)
  }
  return { grid, cellSize }
}

function findClosestRealNodeGrid(nodes, gridObj, virtualNode) {
  const { grid, cellSize } = gridObj
  const cx = Math.floor(virtualNode.x / cellSize)
  const cy = Math.floor(virtualNode.y / cellSize)
  const cz = Math.floor(virtualNode.z / cellSize)
  let minDist2 = Infinity,
    closest = null
  // 只查找周围27个格子
  for (let dx = -1; dx <= 1; dx++)
    for (let dy = -1; dy <= 1; dy++)
      for (let dz = -1; dz <= 1; dz++) {
        const key = [cx + dx, cy + dy, cz + dz].join(',')
        const bucket = grid.get(key)
        if (!bucket) continue
        for (const node of bucket) {
          const dist2 =
            (node.x - virtualNode.x) ** 2 +
            (node.y - virtualNode.y) ** 2 +
            (node.z - virtualNode.z) ** 2
          if (dist2 < minDist2) {
            minDist2 = dist2
            closest = node
          }
        }
      }
  return closest
}

function createBrainConnections(nodes) {
  const links = []
  const linkColors = {
    outline: '#5cffff99',
    surface: '#33ffffaa',
    internal: '#21b8c044',
  }

  // 预先分组
  const surfaceNodes = nodes.filter((n) => n.nodeSize >= 1.4)
  const outlineNodes = nodes.filter((n) => n.nodeSize > 1.8)
  const gridObj = buildSpatialGrid(nodes, 60)

  // 1. 内部连接
  for (let i = 0; i < nodes.length; i += 10) {
    const sourceNode = nodes[i]
    if (sourceNode.nodeSize > 1.8) continue
    // 只查找附近格子
    let minDist2 = 1600,
      nearest = null
    for (const node of nodes) {
      if (node === sourceNode || node.nodeSize > 1.8) continue
      const dx = node.x - sourceNode.x
      const dy = node.y - sourceNode.y
      const dz = node.z - sourceNode.z
      const dist2 = dx * dx + dy * dy + dz * dz
      if (dist2 < minDist2 && dist2 > 0) {
        minDist2 = dist2
        nearest = node
      }
    }
    if (nearest) {
      links.push({
        source: sourceNode.id,
        target: nearest.id,
        color: linkColors.internal,
        width: 0.2,
      })
    }
  }

  // 2. 表面连接
  for (let i = 0; i < surfaceNodes.length; i++) {
    const sourceNode = surfaceNodes[i]
    let minDist2 = 10000,
      nearest = null
    for (const node of surfaceNodes) {
      if (node === sourceNode) continue
      const dx = node.x - sourceNode.x
      const dy = node.y - sourceNode.y
      const dz = node.z - sourceNode.z
      const dist2 = dx * dx + dy * dy + dz * dz
      if (dist2 < minDist2 && dist2 > 0) {
        minDist2 = dist2
        nearest = node
      }
    }
    if (nearest) {
      links.push({
        source: sourceNode.id,
        target: nearest.id,
        color: linkColors.surface,
        width: 0.6,
      })
    }
  }

  // 3. 轮廓连接
  for (let i = 0; i < outlineNodes.length; i++) {
    const nextIdx = (i + 1) % outlineNodes.length
    links.push({
      source: outlineNodes[i].id,
      target: outlineNodes[nextIdx].id,
      color: linkColors.outline,
      width: 1.0,
    })
    if (i % 2 === 0) {
      const crossIdx = (i + 2) % outlineNodes.length
      links.push({
        source: outlineNodes[i].id,
        target: outlineNodes[crossIdx].id,
        color: linkColors.outline,
        width: 0.8,
      })
    }
  }

  // 4. 环形连接（用空间网格加速）
  const ringCount = 2
  for (let r = 0; r < ringCount; r++) {
    const ringNodeCount = 30
    const ringRadius = 300 + r * 80
    const ringTilt = [
      { x: 0.1, y: Math.PI * 0.5, z: 0.1 },
      { x: Math.PI * 0.5, y: 0.1, z: 0.1 },
    ][r]
    const ringNodes = []
    for (let i = 0; i < ringNodeCount; i++) {
      const angle = (i / ringNodeCount) * Math.PI * 2
      let rx = ringRadius * Math.cos(angle)
      let ry = ringRadius * Math.sin(angle)
      let rz = 0
      // 旋转
      let tempY = ry * Math.cos(ringTilt.x) - rz * Math.sin(ringTilt.x)
      let tempZ = ry * Math.sin(ringTilt.x) + rz * Math.cos(ringTilt.x)
      ry = tempY
      rz = tempZ
      let tempX = rx * Math.cos(ringTilt.y) + rz * Math.sin(ringTilt.y)
      tempZ = -rx * Math.sin(ringTilt.y) + rz * Math.cos(ringTilt.y)
      rx = tempX
      rz = tempZ
      tempX = rx * Math.cos(ringTilt.z) - ry * Math.sin(ringTilt.z)
      tempY = rx * Math.sin(ringTilt.z) + ry * Math.cos(ringTilt.z)
      rx = tempX
      ry = tempY
      ringNodes.push({ x: rx, y: ry, z: rz })
    }
    for (let i = 0; i < ringNodeCount; i++) {
      const nextIndex = (i + 1) % ringNodeCount
      const currentNode = ringNodes[i]
      const nextNode = ringNodes[nextIndex]
      const closestToCurrentNode = findClosestRealNodeGrid(
        nodes,
        gridObj,
        currentNode
      )
      const closestToNextNode = findClosestRealNodeGrid(
        nodes,
        gridObj,
        nextNode
      )
      if (closestToCurrentNode && closestToNextNode) {
        links.push({
          source: closestToCurrentNode.id,
          target: closestToNextNode.id,
          color: linkColors.surface,
          width: 0.5,
        })
      }
    }
  }
  return links
}
