<template>
  <div class="realistic-brain-demo">
    <div class="demo-header">
      <h1>🧠 真实大脑形状演示</h1>
      <p>基于解剖图像的大脑3D可视化</p>
      <div class="demo-controls">
        <button @click="generateBrainData" :disabled="isGenerating">
          {{ isGenerating ? '生成中...' : '重新生成大脑' }}
        </button>
        <button @click="toggleRotation">
          {{ isRotating ? '停止旋转' : '开始旋转' }}
        </button>
        <button @click="showAnalysis = !showAnalysis">
          {{ showAnalysis ? '隐藏' : '显示' }}解剖分析
        </button>
      </div>
    </div>

    <div class="demo-content">
      <!-- 3D大脑可视化 -->
      <div class="brain-visualization">
        <force-star
          ref="forceStar"
          :forceData="brainData"
          :bondList="[]">
        </force-star>
        
        <!-- 视角控制 -->
        <div class="view-controls">
          <button @click="setView('front')">正面</button>
          <button @click="setView('side')">侧面</button>
          <button @click="setView('top')">顶部</button>
          <button @click="setView('back')">背面</button>
        </div>
      </div>

      <!-- 解剖分析面板 -->
      <div v-if="showAnalysis" class="anatomy-panel">
        <h3>🔬 解剖结构分析</h3>
        
        <!-- 脑区统计 -->
        <div class="region-stats">
          <h4>脑区分布</h4>
          <div class="stats-grid">
            <div v-for="stat in regionStats" :key="stat.name" class="stat-card">
              <div class="stat-header" :style="{ borderLeftColor: stat.color }">
                <span class="region-name">{{ getRegionDisplayName(stat.name) }}</span>
                <span class="node-count">{{ stat.count }}个</span>
              </div>
              <div class="stat-details">
                <div class="detail-item">
                  <span>密度:</span>
                  <span>{{ stat.density.toFixed(2) }}</span>
                </div>
                <div class="detail-item">
                  <span>占比:</span>
                  <span>{{ (stat.percentage).toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 形状特征 -->
        <div class="shape-features">
          <h4>形状特征</h4>
          <div class="feature-list">
            <div class="feature-item">
              <span class="feature-icon">🧩</span>
              <div class="feature-content">
                <strong>前额叶突出</strong>
                <p>前方圆润突出，体现高级认知功能区域</p>
              </div>
            </div>
            <div class="feature-item">
              <span class="feature-icon">👂</span>
              <div class="feature-content">
                <strong>颞叶扩展</strong>
                <p>侧面和下方扩展，负责听觉和记忆</p>
              </div>
            </div>
            <div class="feature-item">
              <span class="feature-icon">👁️</span>
              <div class="feature-content">
                <strong>枕叶后突</strong>
                <p>后方突出，视觉皮层所在区域</p>
              </div>
            </div>
            <div class="feature-item">
              <span class="feature-icon">🎯</span>
              <div class="feature-content">
                <strong>顶叶圆顶</strong>
                <p>上方圆顶形状，空间处理中心</p>
              </div>
            </div>
            <div class="feature-item">
              <span class="feature-icon">🌊</span>
              <div class="feature-content">
                <strong>皮层褶皱</strong>
                <p>表面沟回结构，增加皮层面积</p>
              </div>
            </div>
            <div class="feature-item">
              <span class="feature-icon">🎪</span>
              <div class="feature-content">
                <strong>小脑褶皱</strong>
                <p>高密度褶皱结构，运动协调中心</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 对比分析 -->
        <div class="comparison-analysis">
          <h4>与简单椭球的对比</h4>
          <div class="comparison-metrics">
            <div class="metric">
              <span class="metric-label">形状复杂度</span>
              <div class="metric-bar">
                <div class="old-value" style="width: 20%">简单椭球: 20%</div>
                <div class="new-value" style="width: 95%">真实形状: 95%</div>
              </div>
            </div>
            <div class="metric">
              <span class="metric-label">解剖准确性</span>
              <div class="metric-bar">
                <div class="old-value" style="width: 15%">简单椭球: 15%</div>
                <div class="new-value" style="width: 90%">真实形状: 90%</div>
              </div>
            </div>
            <div class="metric">
              <span class="metric-label">功能区分化</span>
              <div class="metric-bar">
                <div class="old-value" style="width: 10%">简单椭球: 10%</div>
                <div class="new-value" style="width: 85%">真实形状: 85%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 密度分析面板 -->
    <brain-density-panel 
      :nodes="brainData.nodes" 
      :visible="true"
      @update-visualization="handleVisualizationUpdate"
    />
  </div>
</template>

<script>
import ForceStar from '@/components/force-star/index'
import BrainDensityPanel from '@/components/brain-density-panel/index'
import { calculateBrainPositions } from '@/utils/brainPosition'

export default {
  name: 'RealisticBrainDemo',
  components: {
    ForceStar,
    BrainDensityPanel
  },
  data() {
    return {
      brainData: {
        nodes: [],
        links: []
      },
      isGenerating: false,
      isRotating: false,
      showAnalysis: true,
      regionDisplayNames: {
        frontal: '前额叶',
        parietal: '顶叶',
        temporal: '颞叶',
        occipital: '枕叶',
        cerebellum: '小脑',
        brainstem: '脑干'
      }
    }
  },
  computed: {
    regionStats() {
      const stats = {}
      const totalNodes = this.brainData.nodes.length
      
      this.brainData.nodes.forEach(node => {
        const region = node.brainRegion || 'unknown'
        if (!stats[region]) {
          stats[region] = {
            name: region,
            count: 0,
            totalDensity: 0,
            color: node.color
          }
        }
        stats[region].count++
        stats[region].totalDensity += (node.density || 1.0)
      })

      return Object.values(stats).map(stat => ({
        ...stat,
        density: stat.count > 0 ? stat.totalDensity / stat.count : 0,
        percentage: totalNodes > 0 ? (stat.count / totalNodes) * 100 : 0
      })).sort((a, b) => b.count - a.count)
    }
  },
  mounted() {
    this.generateBrainData()
  },
  methods: {
    async generateBrainData() {
      this.isGenerating = true
      
      // 生成大脑节点数据
      const brainNodes = []
      const nodeCount = 8000 // 增加节点数量以更好展示形状
      
      for (let i = 0; i < nodeCount; i++) {
        brainNodes.push({
          keywords: `神经元${i}`,
          book_keywords_id: i,
          member_keywords_id: i
        })
      }

      // 使用真实大脑形状算法
      const processedNodes = calculateBrainPositions(brainNodes, 0)
      
      this.brainData = {
        nodes: processedNodes,
        links: []
      }

      this.isGenerating = false
    },

    toggleRotation() {
      this.isRotating = !this.isRotating
      if (this.$refs.forceStar) {
        // 这里可以调用3D组件的旋转方法
        this.$refs.forceStar.toggleAutoRotation(this.isRotating)
      }
    },

    setView(viewType) {
      if (this.$refs.forceStar) {
        // 设置不同的观察角度
        const viewAngles = {
          front: { x: 0, y: 0, z: 300 },
          side: { x: 300, y: 0, z: 0 },
          top: { x: 0, y: 300, z: 0 },
          back: { x: 0, y: 0, z: -300 }
        }
        this.$refs.forceStar.setCameraPosition(viewAngles[viewType])
      }
    },

    getRegionDisplayName(region) {
      return this.regionDisplayNames[region] || region
    },

    handleVisualizationUpdate(settings) {
      if (this.$refs.forceStar) {
        this.$refs.forceStar.updateVisualizationSettings(settings)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.realistic-brain-demo {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  font-family: 'Arial', sans-serif;
  overflow: hidden;

  .demo-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    padding: 20px;
    z-index: 1000;
    border-bottom: 2px solid #3498db;
    backdrop-filter: blur(10px);
    
    h1 {
      margin: 0 0 5px 0;
      font-size: 28px;
      color: #3498db;
      text-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
    }
    
    p {
      margin: 0 0 15px 0;
      color: #bdc3c7;
      font-size: 16px;
    }
    
    .demo-controls {
      display: flex;
      gap: 15px;
      
      button {
        padding: 10px 20px;
        background: linear-gradient(45deg, #3498db, #2980b9);
        border: none;
        color: white;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &:hover {
          background: linear-gradient(45deg, #2980b9, #1f618d);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        
        &:disabled {
          background: #7f8c8d;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }

  .demo-content {
    padding-top: 120px;
    display: flex;
    height: calc(100vh - 120px);
  }

  .brain-visualization {
    flex: 1;
    position: relative;
    
    .view-controls {
      position: absolute;
      bottom: 20px;
      left: 20px;
      display: flex;
      gap: 10px;
      
      button {
        padding: 8px 16px;
        background: rgba(52, 152, 219, 0.8);
        border: none;
        color: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        backdrop-filter: blur(5px);
        
        &:hover {
          background: rgba(52, 152, 219, 1);
        }
      }
    }
  }

  .anatomy-panel {
    width: 380px;
    background: rgba(0, 0, 0, 0.9);
    border-left: 2px solid #34495e;
    padding: 25px;
    overflow-y: auto;
    backdrop-filter: blur(10px);
    
    h3 {
      margin: 0 0 20px 0;
      color: #3498db;
      border-bottom: 2px solid #34495e;
      padding-bottom: 10px;
      font-size: 20px;
    }
    
    h4 {
      margin: 25px 0 15px 0;
      color: #ecf0f1;
      font-size: 16px;
    }
  }

  .stats-grid {
    display: grid;
    gap: 12px;
    
    .stat-card {
      background: rgba(52, 73, 94, 0.3);
      border-radius: 6px;
      padding: 12px;
      border-left: 4px solid;
      
      .stat-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .region-name {
          font-weight: bold;
          color: #ecf0f1;
        }
        
        .node-count {
          color: #bdc3c7;
          font-size: 12px;
        }
      }
      
      .stat-details {
        display: flex;
        justify-content: space-between;
        
        .detail-item {
          display: flex;
          flex-direction: column;
          font-size: 11px;
          
          span:first-child {
            color: #95a5a6;
          }
          
          span:last-child {
            color: #ecf0f1;
            font-weight: bold;
          }
        }
      }
    }
  }

  .feature-list {
    .feature-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 15px;
      
      .feature-icon {
        font-size: 20px;
        margin-right: 12px;
        margin-top: 2px;
      }
      
      .feature-content {
        flex: 1;
        
        strong {
          color: #3498db;
          display: block;
          margin-bottom: 4px;
        }
        
        p {
          margin: 0;
          color: #bdc3c7;
          font-size: 13px;
          line-height: 1.4;
        }
      }
    }
  }

  .comparison-metrics {
    .metric {
      margin-bottom: 15px;
      
      .metric-label {
        display: block;
        color: #ecf0f1;
        margin-bottom: 6px;
        font-size: 13px;
      }
      
      .metric-bar {
        position: relative;
        height: 30px;
        background: #2c3e50;
        border-radius: 4px;
        overflow: hidden;
        
        .old-value, .new-value {
          position: absolute;
          height: 50%;
          display: flex;
          align-items: center;
          padding-left: 8px;
          font-size: 10px;
          color: white;
        }
        
        .old-value {
          top: 0;
          background: #7f8c8d;
        }
        
        .new-value {
          bottom: 0;
          background: #27ae60;
        }
      }
    }
  }
}
</style>
