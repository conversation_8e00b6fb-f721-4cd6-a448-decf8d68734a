<template>
  <div class="test-sse">
    <h2>SSE 分批渲染测试</h2>

    <!-- 控制面板 -->
    <div class="control-panel">
      <button @click="startSSETest" :disabled="isRunning">开始SSE测试</button>
      <button @click="stopSSETest" :disabled="!isRunning">停止测试</button>
      <button @click="clearData">清空数据</button>
    </div>

    <!-- 状态显示 -->
    <div class="status-panel">
      <div>状态: {{ isRunning ? '运行中' : '已停止' }}</div>
      <div>节点数量: {{ forceData.nodes.length }}</div>
      <div>待渲染: {{ pendingNodes.length }}</div>
      <div>正在渲染: {{ isRendering ? '是' : '否' }}</div>
      <div>总接收: {{ totalNodesReceived }}</div>
      <div>批次大小: {{ batchSize }}</div>
      <div>渲染延迟: {{ renderDelay }}ms</div>
    </div>

    <!-- 3D图形容器 -->
    <force-star
        ref="forceStar"
        :forceData="forceData"
        :bondList="bondList">
    </force-star>
  </div>
</template>

<script>
import ForceStar from '@/components/force-star/index'

export default {
  name: 'TestSSE',
  components: {
    ForceStar
  },
  data() {
    return {
      forceData: {
        nodes: [],
        links: []
      },
      bondList: [],
      isRunning: false,
      batchSize: 50,
      renderDelay: 200,
      totalNodesReceived: 0,
      isRendering: false,
      brainPositionIndex: 0,
      pendingNodes: [],
      testInterval: null
    }
  },
  methods: {
    // 模拟SSE数据
    startSSETest() {
      this.isRunning = true
      let nodeId = 1

      this.testInterval = setInterval(() => {
        // 模拟每次接收10-50个节点
        const batchSize = Math.floor(Math.random() * 40) + 10
        const mockData = []

        for (let i = 0; i < batchSize; i++) {
          mockData.push({
            keywords: `节点${nodeId}`,
            book_keywords_id: nodeId,
            member_keywords_id: nodeId
          })
          nodeId++
        }

        console.log(`模拟接收到 ${mockData.length} 个节点`)
        this.totalNodesReceived += mockData.length
        this.pendingNodes.push(...mockData)

        if (!this.isRendering) {
          this.startBatchRendering()
        }

        // 模拟接收100批数据后停止
        if (nodeId > 1000) {
          this.stopSSETest()
        }
      }, 500) // 每500ms接收一批数据
    },

    stopSSETest() {
      this.isRunning = false
      if (this.testInterval) {
        clearInterval(this.testInterval)
        this.testInterval = null
      }
    },

    clearData() {
      this.stopSSETest()
      this.$set(this.forceData, 'nodes', [])
      this.$set(this.forceData, 'links', [])
      this.pendingNodes = []
      this.totalNodesReceived = 0
      this.brainPositionIndex = 0
      this.isRendering = false
    },

    // 开始分批渲染
    async startBatchRendering() {
      if (this.isRendering) return

      this.isRendering = true
      console.log('开始分批渲染，待渲染节点数:', this.pendingNodes.length)

      while (this.pendingNodes.length > 0) {
        const batch = this.pendingNodes.splice(0, this.batchSize)
        const processedNodes = this.calculateBrainPositions(batch)

        const oldNodeCount = this.forceData.nodes.length
        // 直接添加到现有数组中
        this.forceData.nodes.push(...processedNodes)

        console.log(`渲染了 ${batch.length} 个节点，从 ${oldNodeCount} 增加到 ${this.forceData.nodes.length}`)

        // 通过事件通知子组件更新图形
        this.$nextTick(() => {
          if (this.$refs.forceStar) {
            this.$refs.forceStar.updateGraphDynamic(processedNodes)
            console.log('TestSSE: 已通知force-star组件更新')
          }
        })

        if (this.pendingNodes.length > 0) {
          await new Promise(resolve => setTimeout(resolve, this.renderDelay))
        }
      }

      this.isRendering = false
      console.log('分批渲染完成，总节点数:', this.forceData.nodes.length)
    },

    // 计算大脑形状位置
    calculateBrainPositions(nodes) {
      const processedNodes = []
      const brainWidth = 420
      const brainHeight = 400
      const brainDepth = 450

      const nodeColors = {
        surface: '#33ffff',
        internal: '#21b8c0',
        highlight: '#5cffff'
      }

      for (let i = 0; i < nodes.length; i++) {
        const item = nodes[i]
        const globalIndex = this.brainPositionIndex + i
        const t = (globalIndex % 1000) / 1000

        let x = 0, y = 0, z = 0
        let nodeSize = 1.0
        let nodeColor = nodeColors.internal

        if (t < 0.75) {
          // 大脑皮层
          const theta = Math.PI * 2 * Math.random()
          const phi = Math.acos(2 * Math.random() - 1)
          const isSurface = Math.random() > 0.7
          let radius

          if (isSurface) {
            radius = 0.85 + 0.15 * Math.random()
            nodeSize = 1.5
            nodeColor = nodeColors.surface
          } else {
            radius = Math.pow(Math.random(), 1/3) * 0.8
            nodeSize = 0.7
          }

          x = radius * Math.sin(phi) * Math.cos(theta)
          y = radius * Math.cos(phi)
          z = radius * Math.sin(phi) * Math.sin(theta)

          x *= 1.1
          if (z > 0 && y > 0) z *= 1.3
          if (z < 0 && Math.abs(y) < 0.3) z *= 1.2

          x *= brainWidth * 0.45
          y *= brainHeight * 0.45
          z *= brainDepth * 0.4
          y += brainHeight * 0.1

        } else if (t < 0.9) {
          // 小脑
          const theta = Math.PI * (0.3 + 0.4 * Math.random())
          const phi = Math.PI * (0.6 + 0.3 * Math.random())
          const isSurface = Math.random() > 0.6
          let radius = isSurface ? 0.85 + 0.15 * Math.random() : Math.pow(Math.random(), 1/3) * 0.7

          x = radius * Math.sin(phi) * Math.cos(theta)
          y = radius * Math.cos(phi)
          z = -Math.abs(radius * Math.sin(phi) * Math.sin(theta))

          x *= brainWidth * 0.25
          y *= brainHeight * 0.25
          z *= brainDepth * 0.25
          y -= brainHeight * 0.15
          z -= brainDepth * 0.15

        } else {
          // 脑干
          const height = Math.random()
          const angle = Math.PI * 2 * Math.random()
          const radius = 0.3 * Math.random()

          x = radius * Math.cos(angle)
          y = -0.5 - height * 0.5
          z = -0.1 - radius * Math.sin(angle)

          x *= brainWidth * 0.15
          y *= brainHeight * 0.4
          z *= brainDepth * 0.2
          y -= brainHeight * 0.15
          z -= brainDepth * 0.1
        }

        const jitter = 5
        x += (Math.random() - 0.5) * jitter
        y += (Math.random() - 0.5) * jitter
        z += (Math.random() - 0.5) * jitter

        processedNodes.push({
          id: Date.now() + Math.random() * 1000000,
          user: item.keywords,
          color: nodeColor,
          bookId: item.book_keywords_id,
          nodeId: item.member_keywords_id,
          x, y, z,
          fx: x, fy: y, fz: z,
          nodeSize
        })
      }

      this.brainPositionIndex += nodes.length
      return processedNodes
    }
  },

  beforeDestroy() {
    this.stopSSETest()
  }
}
</script>

<style scoped>
.test-sse {
  height: 100vh;
  position: relative;
}

.control-panel {
  position: fixed;
  top: 10px;
  left: 10px;
  z-index: 1000;
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 10px;
  border-radius: 5px;
}

.control-panel button {
  margin-right: 10px;
  padding: 5px 10px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.control-panel button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.status-panel {
  position: fixed;
  top: 60px;
  left: 10px;
  z-index: 1000;
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 10px;
  border-radius: 5px;
  font-size: 12px;
}

.status-panel div {
  margin-bottom: 5px;
}
</style>
