# SSE流数据分批渲染到3D大脑形状可视化

## 功能概述

本项目实现了将SSE（Server-Sent Events）流式数据分批渲染到3d-force-graph组件上，并绘制成大脑形状的可视化效果。

## 主要特性

### 1. SSE流式数据处理
- 使用 `@microsoft/fetch-event-source` 库接收SSE数据流
- 实现数据队列管理，避免数据丢失
- 支持连接状态管理和错误处理
- 自动重连机制

### 2. 分批渲染机制
- **批次大小**: 可配置每批处理的节点数量（默认1000个）
- **渲染延迟**: 可配置批次间延迟时间（默认100ms）
- **响应式更新**: 使用Vue 2的 `$set` 方法确保UI正确更新
- **性能优化**: 避免大量数据一次性渲染造成的UI阻塞

### 3. 3D大脑形状布局
#### 大脑解剖结构分布：
- **大脑皮层** (75%节点): 
  - 使用椭球体分布算法
  - 包含前额叶突出 (z > 0 && y > 0)
  - 包含视觉皮层特征 (z < 0 && |y| < 0.3)
  - 表面节点更大更亮，内部节点较小

- **小脑** (15%节点):
  - 位于大脑后下方
  - 具有特殊的褶皱纹理
  - 较小的尺寸和独特的形状

- **脑干** (10%节点):
  - 位于底部中央
  - 连接大脑和小脑
  - 细长的圆柱形结构

#### 视觉效果：
- **颜色区分**: 不同脑区使用不同颜色
  - 表面: `#33ffff` (亮青色)
  - 内部: `#21b8c0` (深青色)  
  - 高亮: `#5cffff` (超亮青色)
- **节点大小**: 根据位置和重要性调整
- **固定位置**: 使用 fx, fy, fz 保持大脑形状稳定
- **新节点动画**: 新出现的节点有黄色高亮效果

### 4. 实时监控面板
显示以下信息：
- 当前节点数量
- 待渲染队列长度
- 渲染状态（是否正在渲染）
- 总接收数据量
- 批次大小和延迟设置

## 文件结构

```
src/
├── views/
│   ├── Index.vue           # 主页面，包含SSE数据处理和分批渲染
│   └── TestSSE.vue         # 测试页面，模拟SSE数据流
├── components/
│   └── force-star/
│       ├── index.vue       # 3D力导向图组件
│       └── utils.js        # 工具函数
└── utils/
    └── getBrainData.js     # 大脑形状位置计算（参考实现）
```

## 核心实现

### 1. SSE数据接收 (Index.vue)
```javascript
sseData() {
  this.sseController = new AbortController()
  
  fetchEventSource('/api/Nebula/keywordsListAll', {
    headers: {
      Authorization: localStorage.getItem('authorization') || '',
    },
    signal: this.sseController.signal,
    onmessage: (ev) => {
      const parsed = JSON.parse(ev.data)
      if (parsed?.length > 0) {
        this.pendingNodes.push(...parsed)
        if (!this.isRendering) {
          this.startBatchRendering()
        }
      }
    }
  })
}
```

### 2. 分批渲染逻辑
```javascript
async startBatchRendering() {
  this.isRendering = true
  
  while (this.pendingNodes.length > 0) {
    const batch = this.pendingNodes.splice(0, this.batchSize)
    const processedNodes = this.calculateBrainPositions(batch)
    
    // 使用Vue 2响应式更新
    this.$set(this.forceData, 'nodes', [...this.forceData.nodes, ...processedNodes])
    
    // 批次间延迟
    if (this.pendingNodes.length > 0) {
      await new Promise(resolve => setTimeout(resolve, this.renderDelay))
    }
  }
  
  this.isRendering = false
}
```

### 3. 大脑形状位置计算
```javascript
calculateBrainPositions(nodes) {
  // 根据节点索引确定脑区分布
  const t = (globalIndex % 1000) / 1000
  
  if (t < 0.75) {
    // 大脑皮层：椭球体分布
  } else if (t < 0.9) {
    // 小脑：后下方分布
  } else {
    // 脑干：中央下方分布
  }
  
  // 添加随机抖动和固定位置
  return processedNodes
}
```

### 4. 3D图形动态更新 (force-star/index.vue)
```javascript
watch: {
  forceData: {
    handler(newData, oldData) {
      if (Graph && newData && newData.nodes) {
        const newNodeCount = newData.nodes.length
        const oldNodeCount = oldData?.nodes?.length || 0
        
        if (newNodeCount !== oldNodeCount) {
          Graph.graphData({
            nodes: [...newData.nodes],
            links: [...(newData.links || [])]
          })
          this.animateNewNodes(newData.nodes)
        }
      }
    },
    deep: true
  }
}
```

## 使用方法

### 1. 正常使用
访问 `/index` 路由，页面会自动连接SSE并开始接收数据。

### 2. 测试模式
访问 `/test-sse` 路由，可以：
- 点击"开始SSE测试"模拟数据流
- 调整批次大小和延迟参数
- 观察实时渲染效果
- 清空数据重新测试

## 配置参数

```javascript
data() {
  return {
    batchSize: 1000,        // 每批渲染节点数量
    renderDelay: 100,       // 批次间延迟(ms)
    // ... 其他配置
  }
}
```

## Vue 2响应式注意事项

由于Vue 2的响应式系统限制，必须使用以下方法更新数组：
- `this.$set(object, key, value)` - 设置对象属性
- `array.push()`, `array.splice()` - 使用数组变异方法
- 避免直接赋值 `array[index] = value`

## 性能优化

1. **分批处理**: 避免一次性渲染大量节点
2. **延迟渲染**: 批次间适当延迟，保持UI响应
3. **条件更新**: 只在节点数量变化时更新3D图形
4. **固定位置**: 使用fx/fy/fz固定节点位置，减少计算
5. **内存管理**: 及时清理SSE连接和定时器

## 浏览器兼容性

- 支持现代浏览器（Chrome 60+, Firefox 55+, Safari 12+）
- 需要WebGL支持（用于3D渲染）
- 需要EventSource支持（用于SSE）

## 故障排除

1. **节点不显示**: 检查控制台是否有响应式更新日志
2. **渲染卡顿**: 减小batchSize或增加renderDelay
3. **SSE连接失败**: 检查网络和授权头
4. **3D图形不更新**: 确认使用了$set方法更新数据

## 扩展功能

可以进一步扩展的功能：
- 添加连接线的动态生成
- 实现不同类型节点的视觉区分
- 添加节点搜索和高亮功能
- 支持数据导出和导入
- 添加性能监控面板
